import { Module } from '@nestjs/common';
import { AdvantagesService } from './advantages.service';
import { AdvantagesController } from './advantages.controller';
import { AdvantageRepository } from './repository/advantage.repository';

@Module({
  controllers: [AdvantagesController],
  providers: [AdvantagesService, AdvantageRepository],
  exports: [AdvantagesService, AdvantageRepository]
})
export class AdvantagesModule { }

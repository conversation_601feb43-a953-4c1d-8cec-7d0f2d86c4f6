import { Injectable } from '@nestjs/common';
import { CreateAdvantageDto } from './dto/create-advantage.dto';
import { UpdateAdvantageDto } from './dto/update-advantage.dto';
import { BaseService } from '@la-pasta/common';
import { AdvantageRepository } from './repository/advantage.repository';

@Injectable()
export class AdvantagesService extends BaseService {

  constructor(private readonly advantagesRepository: AdvantageRepository) {
    super(advantagesRepository);
  }

}
